#!/usr/bin/env python3
"""
WebSocket音频服务器 - 支持WAV文件播放
用于向ESP32客户端发送音频数据
使用真正的Opus编码
"""

import asyncio
import websockets
import struct
import time
import os
import wave
import numpy as np
import threading
from queue import Queue
import sys
import json
import json
try:
    import opuslib
    OPUS_AVAILABLE = True
    print("✓ Opus编码器可用")
except ImportError:
    OPUS_AVAILABLE = False
    print("⚠ Opus编码器不可用，请安装: pip install opuslib")

class WebSocketAudioServer:
    def __init__(self, wav_files=None):
        self.seq_num = 0
        self.client_count = 0
        self.wav_files = wav_files if isinstance(wav_files, list) else [wav_files] if wav_files else []
        self.current_file_index = 0
        self.audio_queue = Queue()
        self.audio_thread = None
        self.is_playing = False
        self.audio_files_data = []  # 存储所有音频文件的数据
        self.connected_clients = set()  # 存储连接的客户端
        self.audio_streaming = False  # 控制音频流发送

        # 初始化Opus编码器
        if OPUS_AVAILABLE:
            try:
                # 16kHz采样率，单声道，VoIP应用
                self.opus_encoder = opuslib.Encoder(16000, 1, opuslib.APPLICATION_VOIP)
                print("✓ Opus编码器初始化成功 (16kHz, 单声道)")
            except Exception as e:
                print(f"✗ Opus编码器初始化失败: {e}")
                self.opus_encoder = None
        else:
            self.opus_encoder = None

        # 如果指定了WAV文件，加载所有音频文件并启动音频处理线程
        if self.wav_files:
            self.load_all_wav_files()
            if self.audio_files_data:
                self.start_audio_thread()
        
    async def handle_client(self, websocket, path):
        self.client_count += 1
        client_addr = websocket.remote_address
        print(f"[{time.strftime('%H:%M:%S')}] Client {self.client_count} connected: {client_addr}")

        # 添加到连接的客户端集合
        self.connected_clients.add(websocket)

        try:
            # 发送欢迎JSON消息
            welcome_msg = {
                "type": "notification",
                "timestamp": int(time.time()),
                "data": {
                    "event": "connected",
                    "message": "Welcome to WebSocket Audio Server",
                    "server_time": time.strftime('%Y-%m-%d %H:%M:%S'),
                    "audio_streaming": self.audio_streaming
                }
            }
            await websocket.send(json.dumps(welcome_msg))
            print(f"[{time.strftime('%H:%M:%S')}] Sent welcome message to {client_addr}")

            # 只启动消息监听任务，音频发送由命令控制
            message_task = asyncio.create_task(self.handle_messages(websocket, client_addr))

            # 如果设置了自动播放音频文件，则启动音频任务
            if self.wav_files and self.audio_files_data:
                audio_task = asyncio.create_task(self.send_audio_stream(websocket, client_addr))
                # 等待任一任务完成
                done, pending = await asyncio.wait([audio_task, message_task], return_when=asyncio.FIRST_COMPLETED)
                # 取消未完成的任务
                for task in pending:
                    task.cancel()
            else:
                # 只等待消息任务
                await message_task

        except websockets.exceptions.ConnectionClosed:
            print(f"[{time.strftime('%H:%M:%S')}] Client disconnected: {client_addr}")
        except Exception as e:
            print(f"[{time.strftime('%H:%M:%S')}] Error with client {client_addr}: {e}")
        finally:
            self.connected_clients.discard(websocket)
            self.client_count -= 1

    async def send_audio_stream(self, websocket, client_addr):
        """发送音频流"""
        try:
            while self.audio_streaming:
                # 获取音频数据（从WAV文件或模拟数据）
                opus_data = self.get_audio_data()

                if opus_data is None:
                    # 没有更多音频数据，等待或重新开始
                    if self.wav_files and self.audio_files_data:
                        print(f"[{time.strftime('%H:%M:%S')}] WAV file finished, restarting...")
                        self.restart_audio()
                        continue
                    else:
                        # 如果没有WAV文件，生成静音数据进行Opus编码
                        opus_data = self.get_simulated_opus_data()

                # 创建WebSocket音频包
                packet = self.create_audio_packet(opus_data)

                # 发送二进制数据
                await websocket.send(packet)

                self.seq_num += 1
                if self.seq_num % 50 == 0:  # 每秒打印一次状态（50帧 * 20ms = 1秒）
                    print(f"[{time.strftime('%H:%M:%S')}] Sent {self.seq_num} audio packets to {client_addr}")

                # 20ms间隔，模拟实时音频流
                await asyncio.sleep(0.02)

        except websockets.exceptions.ConnectionClosed:
            print(f"[{time.strftime('%H:%M:%S')}] Audio stream disconnected: {client_addr}")
        except Exception as e:
            print(f"[{time.strftime('%H:%M:%S')}] Audio stream error with {client_addr}: {e}")

    async def handle_messages(self, websocket, client_addr):
        """处理来自客户端的消息"""
        try:
            async for message in websocket:
                if isinstance(message, str):
                    # 处理文本消息（JSON）
                    await self.handle_text_message(websocket, message, client_addr)
                elif isinstance(message, bytes):
                    # 处理二进制消息（如果需要）
                    print(f"[{time.strftime('%H:%M:%S')}] Received binary message from {client_addr}, length: {len(message)}")
        except websockets.exceptions.ConnectionClosed:
            print(f"[{time.strftime('%H:%M:%S')}] Message handler disconnected: {client_addr}")
        except Exception as e:
            print(f"[{time.strftime('%H:%M:%S')}] Message handler error with {client_addr}: {e}")

    async def handle_text_message(self, websocket, message, client_addr):
        """处理文本消息（JSON）"""
        try:
            data = json.loads(message)
            print(f"[{time.strftime('%H:%M:%S')}] Received JSON from {client_addr}: {data}")

            # 根据消息类型处理
            msg_type = data.get('type', '')

            if msg_type == 'command':
                await self.handle_command(websocket, data, client_addr)
            elif msg_type == 'status':
                await self.handle_status_request(websocket, data, client_addr)
            else:
                print(f"[{time.strftime('%H:%M:%S')}] Unknown message type: {msg_type}")

        except json.JSONDecodeError as e:
            print(f"[{time.strftime('%H:%M:%S')}] Invalid JSON from {client_addr}: {e}")
        except Exception as e:
            print(f"[{time.strftime('%H:%M:%S')}] Error handling text message from {client_addr}: {e}")

    async def handle_command(self, websocket, data, client_addr):
        """处理命令消息"""
        cmd_data = data.get('data', {})
        cmd = cmd_data.get('cmd', '')
        seq = data.get('seq', 0)

        print(f"[{time.strftime('%H:%M:%S')}] Processing command '{cmd}' from {client_addr}")

        result = "success"
        message = f"Command '{cmd}' processed successfully"

        # 处理具体命令
        if cmd == "start_audio":
            self.audio_streaming = True
            message = "Audio streaming started"
            # 启动音频发送任务
            if self.wav_files and self.audio_files_data:
                asyncio.create_task(self.send_audio_stream(websocket, client_addr))
        elif cmd == "stop_audio":
            self.audio_streaming = False
            message = "Audio streaming stopped"
        elif cmd == "get_status":
            message = "Status retrieved"
        else:
            result = "error"
            message = f"Unknown command: {cmd}"

        # 生成响应
        response = {
            "type": "response",
            "seq": seq,
            "timestamp": int(time.time()),
            "result": result,
            "message": message,
            "data": {
                "command": cmd,
                "audio_streaming": self.audio_streaming,
                "processed_at": time.strftime('%Y-%m-%d %H:%M:%S')
            }
        }

        await websocket.send(json.dumps(response))
        print(f"[{time.strftime('%H:%M:%S')}] Sent response to {client_addr}")

    async def handle_status_request(self, websocket, data, client_addr):
        """处理状态请求"""
        seq = data.get('seq', 0)

        status_data = {
            "server_status": "running",
            "connected_clients": len(self.connected_clients),
            "audio_files": len(self.audio_files_data),
            "current_file": self.current_file_index if self.audio_files_data else None,
            "seq_num": self.seq_num,
            "audio_streaming": self.audio_streaming,
            "uptime": time.strftime('%Y-%m-%d %H:%M:%S')
        }

        response = {
            "type": "response",
            "seq": seq,
            "timestamp": int(time.time()),
            "result": "success",
            "message": "Status retrieved successfully",
            "data": status_data
        }

        await websocket.send(json.dumps(response))
        print(f"[{time.strftime('%H:%M:%S')}] Sent status response to {client_addr}")

    async def broadcast_json_message(self, message):
        """向所有连接的客户端广播JSON消息"""
        if not self.connected_clients:
            return

        json_str = json.dumps(message)
        disconnected = set()

        for websocket in self.connected_clients:
            try:
                await websocket.send(json_str)
            except websockets.exceptions.ConnectionClosed:
                disconnected.add(websocket)
            except Exception as e:
                print(f"[{time.strftime('%H:%M:%S')}] Error broadcasting to client: {e}")
                disconnected.add(websocket)

        # 移除断开连接的客户端
        self.connected_clients -= disconnected

    def send_test_json_messages(self):
        """发送测试JSON消息的方法"""
        async def send_messages():
            # 发送开始音频命令
            start_audio_msg = {
                "type": "command",
                "seq": 1001,
                "timestamp": int(time.time()),
                "data": {
                    "cmd": "start_audio"
                }
            }
            await self.broadcast_json_message(start_audio_msg)
            print(f"[{time.strftime('%H:%M:%S')}] Sent start_audio command")

            await asyncio.sleep(3)

            # 发送停止音频命令
            stop_audio_msg = {
                "type": "command",
                "seq": 1002,
                "timestamp": int(time.time()),
                "data": {
                    "cmd": "stop_audio"
                }
            }
            await self.broadcast_json_message(stop_audio_msg)
            print(f"[{time.strftime('%H:%M:%S')}] Sent stop_audio command")

            await asyncio.sleep(2)

            # 发送状态查询
            status_msg = {
                "type": "command",
                "seq": 1003,
                "timestamp": int(time.time()),
                "data": {
                    "cmd": "get_status"
                }
            }
            await self.broadcast_json_message(status_msg)
            print(f"[{time.strftime('%H:%M:%S')}] Sent get_status command")

        # 在事件循环中运行
        asyncio.create_task(send_messages())
    
    def create_audio_packet(self, opus_data):
        """创建WebSocket音频包"""
        version = 0x01
        audio_type = 0x01
        payload_len = len(opus_data)
        reserved = 0x00
        
        # 打包头部（小端序，与ESP32一致）
        header = struct.pack('<BBHHH', 
                           version,        # version
                           audio_type,     # type
                           self.seq_num & 0xFFFF,  # seqNum
                           payload_len,    # payloadLen
                           reserved)       # resv
        
        return header + opus_data
    
    def load_all_wav_files(self):
        """加载所有WAV文件"""
        self.audio_files_data = []
        for wav_file in self.wav_files:
            if os.path.exists(wav_file):
                try:
                    with wave.open(wav_file, 'rb') as wav:
                        sample_rate = wav.getframerate()
                        channels = wav.getnchannels()
                        sample_width = wav.getsampwidth()

                        print(f"WAV文件信息:")
                        print(f"  文件: {wav_file}")
                        print(f"  采样率: {sample_rate} Hz")
                        print(f"  声道数: {channels}")
                        print(f"  位深: {sample_width * 8} bit")

                        # 读取所有音频数据
                        frames = wav.readframes(wav.getnframes())

                        # 转换为numpy数组
                        if sample_width == 1:
                            dtype = np.uint8
                        elif sample_width == 2:
                            dtype = np.int16
                        else:
                            dtype = np.int32

                        audio_data = np.frombuffer(frames, dtype=dtype)

                        # 如果是立体声，转换为单声道
                        if channels == 2:
                            audio_data = audio_data.reshape(-1, 2)
                            audio_data = np.mean(audio_data, axis=1).astype(dtype)

                        # 重采样到16kHz（如果需要）
                        if sample_rate != 16000:
                            audio_data = self.resample_audio(audio_data, sample_rate, 16000)

                        self.audio_files_data.append({
                            'file': wav_file,
                            'data': audio_data,
                            'pos': 0
                        })

                        print(f"  处理后: 16000 Hz, 单声道, {len(audio_data)} 采样点")
                        print(f"  时长: {len(audio_data) / 16000:.2f} 秒")

                except Exception as e:
                    print(f"加载WAV文件失败: {wav_file} - {e}")
            else:
                print(f"WAV文件不存在: {wav_file}")

        # 设置当前播放的音频文件
        if self.audio_files_data:
            self.current_audio_data = self.audio_files_data[0]['data']
            self.current_audio_pos = 0
            print(f"已加载 {len(self.audio_files_data)} 个音频文件")

    def resample_audio(self, audio_data, orig_sr, target_sr):
        """简单的重采样（线性插值）"""
        if orig_sr == target_sr:
            return audio_data

        # 计算重采样比例
        ratio = target_sr / orig_sr
        new_length = int(len(audio_data) * ratio)

        # 线性插值重采样
        old_indices = np.linspace(0, len(audio_data) - 1, new_length)
        new_audio = np.interp(old_indices, np.arange(len(audio_data)), audio_data)

        return new_audio.astype(audio_data.dtype)

    def start_audio_thread(self):
        """启动音频处理线程"""
        self.is_playing = True
        self.audio_thread = threading.Thread(target=self.audio_worker)
        self.audio_thread.daemon = True
        self.audio_thread.start()

    def audio_worker(self):
        """音频处理工作线程 - 使用真正的Opus编码"""
        frame_size = 320  # 16kHz * 0.02s = 320 samples per 20ms frame

        while self.is_playing and self.audio_files_data:
            current_file_data = self.audio_files_data[self.current_file_index]
            audio_data = current_file_data['data']
            audio_pos = current_file_data['pos']

            if audio_pos + frame_size >= len(audio_data):
                # 当前音频文件播放完毕，切换到下一个文件
                print(f"[{time.strftime('%H:%M:%S')}] 音频文件播放完毕: {current_file_data['file']}")
                self.current_file_index = (self.current_file_index + 1) % len(self.audio_files_data)
                self.audio_files_data[self.current_file_index]['pos'] = 0
                print(f"[{time.strftime('%H:%M:%S')}] 切换到音频文件: {self.audio_files_data[self.current_file_index]['file']}")
                continue

            # 获取一帧音频数据
            frame = audio_data[audio_pos:audio_pos + frame_size]
            # 更新播放位置
            self.audio_files_data[self.current_file_index]['pos'] = audio_pos + frame_size

            # 确保帧大小正确
            if len(frame) < frame_size:
                # 如果不足一帧，用零填充
                padded_frame = np.zeros(frame_size, dtype=np.int16)
                padded_frame[:len(frame)] = frame.astype(np.int16)
                frame = padded_frame
            else:
                frame = frame.astype(np.int16)

            # 使用真正的Opus编码
            opus_data = self.encode_to_opus(frame)

            if opus_data:
                # 放入队列
                self.audio_queue.put(opus_data)
            else:
                print(f"[{time.strftime('%H:%M:%S')}] Opus编码失败，跳过此帧")

            # 20ms间隔
            time.sleep(0.02)

    def encode_to_opus(self, pcm_frame):
        """使用真正的Opus编码器编码PCM数据"""
        if not self.opus_encoder:
            print("Opus编码器不可用，使用伪编码")
            return self.encode_to_pseudo_opus(pcm_frame.tobytes())

        try:
            # 确保输入是320个样本的int16数组
            if len(pcm_frame) != 320:
                print(f"警告: 帧大小不正确 {len(pcm_frame)}, 期望320")
                return None

            # Opus编码 - 输入必须是bytes格式
            pcm_bytes = pcm_frame.astype(np.int16).tobytes()
            opus_data = self.opus_encoder.encode(pcm_bytes, 320)

            if len(opus_data) > 0:
                return opus_data
            else:
                print("Opus编码返回空数据")
                return None

        except Exception as e:
            print(f"Opus编码失败: {e}")
            return None

    def encode_to_pseudo_opus(self, pcm_data):
        """备用的伪Opus编码（当真正的Opus不可用时）"""
        # 添加伪Opus头部标识
        opus_header = b'\xFC\x00'  # 伪Opus标识

        # 保持完整的PCM数据，不要截断
        max_payload = min(len(pcm_data), 640)  # 最大640字节（320样本*2字节）

        return opus_header + pcm_data[:max_payload]

    def get_audio_data(self):
        """获取音频数据"""
        if self.wav_files and self.audio_files_data and not self.audio_queue.empty():
            return self.audio_queue.get()
        return None

    def restart_audio(self):
        """重新开始音频播放"""
        if hasattr(self, 'audio_data'):
            self.audio_pos = 0

    def get_simulated_opus_data(self):
        """获取模拟的Opus数据（当没有WAV文件时使用）"""
        if self.opus_encoder:
            # 生成静音PCM数据（320个样本）
            silence_frame = np.zeros(320, dtype=np.int16)

            # 添加一些轻微的噪声，避免完全静音
            noise = np.random.randint(-100, 100, 320, dtype=np.int16)
            silence_frame = silence_frame + noise

            # 使用真正的Opus编码
            return self.encode_to_opus(silence_frame)
        else:
            # 备用：生成伪Opus数据
            frame_size = 60  # 固定大小
            opus_data = bytearray(frame_size)

            if frame_size > 0:
                opus_data[0] = 0xFC  # Opus帧开始标志（模拟）
            if frame_size > 1:
                opus_data[1] = frame_size & 0xFF

            for i in range(2, frame_size):
                opus_data[i] = (self.seq_num + i) & 0xFF

            return bytes(opus_data)

async def main():
    import sys

    # 检查命令行参数
    wav_files = []
    if len(sys.argv) > 1:
        # 支持多个WAV文件作为参数
        for arg in sys.argv[1:]:
            if os.path.exists(arg):
                wav_files.append(arg)
            else:
                print(f"警告: WAV文件不存在: {arg}")
        
        if not wav_files:
            print("错误: 没有有效的WAV文件")
            return

    # 创建服务器实例
    server = WebSocketAudioServer(wav_files if wav_files else None)

    # 服务器配置（与ESP32配置一致）
    host = "************"  # 监听所有接口
    port = 8768       # 端口号（与ESP32配置一致）

    print("=" * 60)
    print("WebSocket音频服务器启动")
    print(f"监听地址: ws://{host}:{port}")
    print(f"启动时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    if wav_files:
        print(f"音频文件: {', '.join(wav_files)}")
    else:
        print("音频模式: 模拟数据")
    print("=" * 60)
    print("等待ESP32客户端连接...")
    print("按 Ctrl+C 停止服务器")
    print("命令:")
    print("  输入 'test' 发送测试JSON消息")
    print("  输入 'start' 启动音频流")
    print("  输入 'stop' 停止音频流")
    print("  输入 'status' 查看服务器状态")
    print()

    try:
        # 启动WebSocket服务器
        async with websockets.serve(server.handle_client, host, port):
            # 启动命令行输入处理
            input_task = asyncio.create_task(handle_user_input(server))
            await input_task
    except KeyboardInterrupt:
        print("\n服务器停止")
        server.is_playing = False
    except Exception as e:
        print(f"服务器错误: {e}")
        server.is_playing = False

async def handle_user_input(server):
    """处理用户输入命令"""
    import sys

    def input_reader():
        while True:
            try:
                return input()
            except EOFError:
                return None

    loop = asyncio.get_event_loop()

    while True:
        try:
            # 在线程池中运行阻塞的input()
            user_input = await loop.run_in_executor(None, input_reader)

            if user_input is None:
                break

            user_input = user_input.strip().lower()

            if user_input == 'test':
                print(f"[{time.strftime('%H:%M:%S')}] 发送测试JSON消息...")
                server.send_test_json_messages()
            elif user_input == 'start':
                print(f"[{time.strftime('%H:%M:%S')}] 启动音频流...")
                server.audio_streaming = True
            elif user_input == 'stop':
                print(f"[{time.strftime('%H:%M:%S')}] 停止音频流...")
                server.audio_streaming = False
            elif user_input == 'status':
                print(f"[{time.strftime('%H:%M:%S')}] 服务器状态:")
                print(f"  连接客户端数: {len(server.connected_clients)}")
                print(f"  音频文件数: {len(server.audio_files_data)}")
                print(f"  当前序列号: {server.seq_num}")
                print(f"  音频流状态: {'发送中' if server.audio_streaming else '停止'}")
                print(f"  播放状态: {'播放中' if server.is_playing else '停止'}")
            elif user_input == 'help':
                print("可用命令:")
                print("  test   - 发送测试JSON消息")
                print("  start  - 启动音频流")
                print("  stop   - 停止音频流")
                print("  status - 显示服务器状态")
                print("  help   - 显示帮助")
                print("  quit   - 退出服务器")
            elif user_input == 'quit':
                print("正在退出服务器...")
                break
            elif user_input:
                print(f"未知命令: {user_input}，输入 'help' 查看帮助")

        except Exception as e:
            print(f"处理用户输入时出错: {e}")
            break

if __name__ == "__main__":
    # 检查依赖
    try:
        import websockets
        import numpy as np
    except ImportError as e:
        print("错误: 缺少必要的库")
        print("请运行以下命令安装依赖:")
        print("pip install websockets numpy opuslib")
        print(f"具体错误: {e}")
        exit(1)

    # 显示使用说明
    if len(sys.argv) == 1:
        print("使用方法:")
        print("  python3 websocket_audio_server.py [WAV文件路径]...")
        print("")
        print("示例:")
        print("  python3 websocket_audio_server.py audio.wav")
        print("  python3 websocket_audio_server.py audio1.wav audio2.wav audio3.wav")
        print("  python3 websocket_audio_server.py  # 使用模拟音频数据")
        print("")

    # 运行服务器
    asyncio.run(main())
