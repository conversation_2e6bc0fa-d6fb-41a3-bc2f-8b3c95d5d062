/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_rlink.c
 * @description: Relay链路, 用于和转发模块间传递音频和控制数据.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdint.h>
#include <stdlib.h>
#include <arpa/inet.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "sdkconfig.h"
#include "esp_log.h"
#include <string.h>
#include <unistd.h>
#include <sys/socket.h>
#include <errno.h>
#include "sk_common.h"
#include "sk_websocket.h"
#include "sk_frame.h"
#include "sk_audio_buffer.h"
#include "sk_rlink.h"
#include "sk_os.h"

#define RLINK_MSG_QUEUE_SIZE 64
#define TAG "SkRlink"

enum {
    RLINK_SOCK_NOT_RDY = 0,
    RLINK_SOCK_RDY = 1,
};

typedef struct {
    uint8_t event;      // 事件
    uint8_t src;        // 事件源模块
    uint16_t param1;    // 参数
    uint32_t timestamp;
    void    *arg;       // 数据长度
} RlinkMsg;

typedef struct {
    // 基本连接管理
    uint32_t linkFlag;
    char serverIp[16];
    uint16_t port;
    int sock;
    uint16_t sockRdy;
    uint8_t taskFlag;
    TaskHandle_t txTaskHandle;
    QueueHandle_t msgQueue;
    void *recordQueue;
    void *decPrivate;
    uint16_t seqID;
    uint16_t sessionID;
    uint32_t sendSuccBytes;
    uint32_t sendFailBytes;

    uint32_t recvDataCnt;
    uint32_t recvBufFailCnt;
    uint32_t recvEnqueueFailCnt;
    uint32_t recvEnqueueCnt;
    SkRlinkCodedDataCallback codedDataCallback;
    SkRlinkCodedDataEndCallback codedDataEndCallback;
} RlinkCtrlInfo;

RlinkCtrlInfo g_rlinkCtrl = {
    .linkFlag = RLINK_LINK_STOP,
    .sockRdy = RLINK_SOCK_NOT_RDY,
    .taskFlag = RLINK_TASK_RUN,
    .sock = -1,
    .msgQueue = NULL,
};

int RlinkConnect(RlinkCtrlInfo *ctrl) {
	int ret, sock;
	uint32_t addr;
    struct sockaddr_in ack_sock_addr;

    memset(&ack_sock_addr, 0, sizeof(struct sockaddr));
    ack_sock_addr.sin_family = AF_INET;
    sock = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);

	addr = inet_addr(ctrl->serverIp);
	memcpy((char *)&ack_sock_addr.sin_addr, (char *)&addr, sizeof(addr));
    ack_sock_addr.sin_port = htons(ctrl->port);
    struct timeval send_timeout = {0, 100000};

    if (setsockopt(sock, SOL_SOCKET, SO_SNDTIMEO, &send_timeout, sizeof(send_timeout)) < 0) {
        perror("Failed to set SO_SNDTIMEO");
        close(sock);
        return -1;
    }

	ret = connect(sock, (struct sockaddr *)&ack_sock_addr, sizeof(struct sockaddr));
    if (ret != 0) {
        ESP_LOGI(TAG, "connect ack failed! socket num=%d", sock);
        closesocket(sock);
        return SK_RET_FAIL;
    }
	ctrl->sock = sock;
	ctrl->sockRdy = RLINK_SOCK_RDY;
    ctrl->sendSuccBytes = 0;
    ctrl->sendFailBytes = 0;

    return SK_RET_SUCCESS;
}

void RlinkDisconnect(RlinkCtrlInfo *ctrl) {
	if (ctrl->sockRdy == RLINK_SOCK_RDY) {
		ctrl->sockRdy = RLINK_SOCK_NOT_RDY;
	    closesocket(ctrl->sock);
		ctrl->sock = -1;
	}

    return;
}

int RlinkSendInnerEvent(RlinkCtrlInfo *ctrl, uint8_t event, uint16_t param1) {
    RlinkMsg msg;
    if (g_rlinkCtrl.msgQueue == NULL) {
        ESP_LOGI(TAG, "send inner event %d, msgQueue is null", event);
        return SK_RET_FAIL;
    }
    msg.event = event;
    msg.src = 0;
    msg.arg = NULL;
    msg.param1 = param1;
    if (xQueueSend(ctrl->msgQueue, &msg, portMAX_DELAY) != pdPASS) {
        ESP_LOGI(TAG, "send inner event %d success, error", event);
        return SK_RET_FAIL;
    }
    ESP_LOGI(TAG, "send inner event %d success", event);
    return SK_RET_SUCCESS;
}

int SkRlinkEventNotify(uint8_t event, uint16_t param1) {
    RlinkMsg msg;
    if (g_rlinkCtrl.msgQueue == NULL) {
        return SK_RET_FAIL;
    }
    msg.event = event;
    msg.param1 = param1;
    msg.arg = NULL;
    xQueueSend(g_rlinkCtrl.msgQueue, &msg, portMAX_DELAY);
    return SK_RET_SUCCESS;
}

int SkRlinkSendAudioData(SkAudioBuf *audioBuf, uint32_t timestamp) {
    RlinkMsg msg;
    if (g_rlinkCtrl.msgQueue == NULL) {
        return SK_RET_FAIL;
    }
    msg.event = RLINK_EVENT_TX_DATA;
    msg.src = 0;
    msg.arg = audioBuf;
    msg.timestamp = timestamp;
    if (xQueueSend(g_rlinkCtrl.msgQueue, &msg, 0) != pdPASS) {
        return SK_RET_FAIL;
    }
    return SK_RET_SUCCESS;
}

int RlinkSendRemoteMsg(RlinkCtrlInfo *ctrl, void *data, size_t size) {
    int ret;

    ret = send(ctrl->sock, (char *)data, size, 0);
    if (ret != size) {
        return SK_RET_FAIL;
    }
    return SK_RET_SUCCESS;
}

inline uint16_t RlinkGetTxSeqId(RlinkCtrlInfo *ctrl) {
    ctrl->seqID++;
    return ctrl->seqID;
}

int32_t RlinkSendAudioFrame(RlinkCtrlInfo *ctrl, DataFrame *frame, uint32_t dataLen, uint32_t timestamp) {
    int ret;
    uint16_t seq;
    SkAudioUplinkTimeRecord *timeRecord = (SkAudioUplinkTimeRecord *) frame->timeRecord;

    seq = RlinkGetTxSeqId(ctrl);
    timeRecord->seq = seq;
    timeRecord->len = dataLen;
    timeRecord->dataFlag = frame->data[1];
    timeRecord->encDoneTick = timestamp;
    timeRecord->encTxTick = SkOsGetTickCnt();
    EncodeRelayDataMsg(frame, dataLen + sizeof(frame->timeRecord));
    frame->frameHead.seqID = htons(seq);

    ret = RlinkSendRemoteMsg(ctrl, frame, dataLen + sizeof(FrameHead) + sizeof(frame->timeRecord));
    if (ret != SK_RET_SUCCESS) {
        // 简化错误处理：连续失败时停止链路
        static int failCnt = 0;
        failCnt++;
        if (failCnt >= 8) {
            ctrl->linkFlag = RLINK_LINK_STOP;
        }
    } 

    return ret;
}

int RlinkSendAudioData(RlinkCtrlInfo *ctrl, SkAudioBuf *audioBuf, uint32_t timestamp) {
    int ret = SK_RET_SUCCESS;

    if (ctrl->linkFlag == RLINK_LINK_RUN) {
        ret = RlinkSendAudioFrame(ctrl, (DataFrame *)audioBuf->data,
            audioBuf->length, timestamp);
    }
    SkAudioBufferPutFree(ctrl->recordQueue, audioBuf);

    return ret;
}

int RlinkInit() {
    RlinkCtrlInfo *ctrl = &g_rlinkCtrl;

    ctrl->msgQueue = xQueueCreate(RLINK_MSG_QUEUE_SIZE, sizeof(RlinkMsg));
    ctrl->recordQueue = SkCreateAudioQueue(4, sizeof(DataFrame), sizeof(FrameHead));

    return SK_RET_SUCCESS;
}

void RlinkDeinit(void) {
    RlinkCtrlInfo *ctrl = &g_rlinkCtrl;
    vQueueDelete(ctrl->msgQueue);
}

int RlinkProcLocalMsg(RlinkCtrlInfo *ctrl, TickType_t ticks) {
    int ret = SK_RET_SUCCESS;
    RlinkMsg msg;

    if (xQueueReceive(ctrl->msgQueue, &msg, ticks) != pdPASS) {
        return ret;
    }
    if (msg.event != RLINK_EVENT_TX_DATA) {
        ESP_LOGD(TAG, "RlinkProcLocalMsg: event = %d", msg.event);
    }
    switch (msg.event) {

        case RLINK_EVENT_STOP_CALL:
            ctrl->linkFlag = RLINK_LINK_STOP;
            break;

        case RLINK_EVENT_TX_DATA:
            RlinkSendAudioData(ctrl, (SkAudioBuf *)msg.arg, msg.timestamp);
            break;
        
        default:
            ESP_LOGI(TAG, "Unknown event:%d", msg.event);
    }

    return ret;
}

void SkRlinkSetFunFlag(uint8_t flag) {
    g_rlinkCtrl.taskFlag = flag;
}

void RlinkMainTask(void *arg) {
    RlinkCtrlInfo *ctrl = &g_rlinkCtrl;
    TickType_t ticks = pdMS_TO_TICKS(100);

    while (ctrl->taskFlag == RLINK_TASK_RUN) {
        // 检查 队列中是否有消息
        if (RlinkProcLocalMsg(ctrl, ticks) != SK_RET_SUCCESS) {
            break;
        }
    }
    vTaskDelete(NULL);
}


void SkRlinkFeedReordAudio(void *handler, const uint8_t *data, size_t len, uint32_t timestamp) {
    RlinkCtrlInfo *ctrl = (RlinkCtrlInfo *)handler;
    SkAudioBuf *audioBuf;

    if (ctrl->linkFlag != RLINK_LINK_RUN) {
        return;
    }
    audioBuf = SkAudioBufferGetFree(ctrl->recordQueue, 0);
    if (audioBuf == NULL) {
        ESP_LOGE(TAG, "Failed to get audio buffer");
        return;
    }
    if (audioBuf->size < (len + audioBuf->offset + 32)) {
        ESP_LOGE(TAG, "SkRlinkFeedReordAudio: audioBuf->size=%d < len=%u", audioBuf->size, len);
        SkAudioBufferPutFree(ctrl->recordQueue, audioBuf);
        return;
    }
    memcpy((int8_t *)&audioBuf->data[audioBuf->offset + 32], data, len);
    audioBuf->length = len;
    if (SkRlinkSendAudioData(audioBuf, timestamp) != SK_RET_SUCCESS) {
        ESP_LOGE(TAG, "SkRlinkSendAudioData failed!");
        SkAudioBufferPutFree(ctrl->recordQueue, audioBuf);
    }
    return;
}

void SkRlinkStartTasks() {
    g_rlinkCtrl.taskFlag = RLINK_TASK_RUN;
    xTaskCreate(RlinkMainTask, "RlinkMainTask", 4096, NULL, 5, &g_rlinkCtrl.txTaskHandle);
    ESP_LOGI(TAG, "stack base %p", pxTaskGetStackStart(g_rlinkCtrl.txTaskHandle));
    return;
}

void SkRlinkInit() {
    if (RlinkInit() != SK_RET_SUCCESS) {
        return;
    }
    SkRlinkStartTasks();

    return;
}


void RlinkPlayAudioData(RlinkCtrlInfo *ctrl, uint8_t *data, uint16_t payloadLen) {
    int32_t ret;
    DataFrame *frame = (DataFrame *)data;
    SkAudioDownlinkTimeRecord *timeRecord = (SkAudioDownlinkTimeRecord *)frame->timeRecord;
    SkAudioDownlinkTimeRecord audioTickRecord;

    ctrl->recvDataCnt++;
    if (ctrl->codedDataCallback == NULL) {
        return;
    }
    memcpy(&audioTickRecord, timeRecord, sizeof(SkAudioDownlinkTimeRecord));
    audioTickRecord.decRxTick = SkOsGetTickCnt();
    ret = ctrl->codedDataCallback(ctrl->decPrivate, ctrl->sessionID, 
        data + sizeof(FrameHead) + sizeof(frame->timeRecord), 
        payloadLen - sizeof(frame->timeRecord), &audioTickRecord);
    if (ret == SK_RET_SUCCESS) {
        ctrl->recvEnqueueCnt++;
    } else if (ret == SK_RET_NO_MEMORY) {
        ctrl->recvBufFailCnt++;
    } else if (ret == SK_RET_INVALID_PARAM) {
        ctrl->recvEnqueueFailCnt++;
    }

    return;
}

void SkRlinkSetCodedDataCallback(SkRlinkCodedDataCallback callback, void *private) {
    RlinkCtrlInfo *ctrl = &g_rlinkCtrl;
    ctrl->codedDataCallback = callback;
    ctrl->decPrivate = private;
    return;
}

void SkRlinkSetCodedDataEndCallback(SkRlinkCodedDataEndCallback callback, void *private) {
    RlinkCtrlInfo *ctrl = &g_rlinkCtrl;
    ctrl->codedDataEndCallback = callback;
    ctrl->decPrivate = private;
    return;
}

SkRlinkHandler SkRlinkGetHandler() {
    return &g_rlinkCtrl;
}

void SkRlinkFeedWebSocketAudio(void *arg, void *data, uint16_t len) {
    RlinkCtrlInfo *ctrl = &g_rlinkCtrl;
    SkWsBinaryHeader_t *pkt;
    uint8_t *frameBuffer = NULL;
    DataFrame *frame = NULL;
    SkAudioDownlinkTimeRecord *timeRecord;
    uint16_t totalLen;
    const uint16_t headerSize = sizeof(SkWsBinaryHeader_t);  // 8字节头部

    // 数据有效性检查
    if (data == NULL || len < headerSize) {
        ESP_LOGE(TAG, "Invalid WebSocket audio data: len=%d, min_len=%d", len, headerSize);
        return;
    }

    pkt = (SkWsBinaryHeader_t *)data;

    // 验证数据格式
    if (pkt->version != 1 || pkt->type != 1) {
        ESP_LOGE(TAG, "Invalid WS packet: ver=%d, type=%d", pkt->version, pkt->type);
        return;
    }

    // 验证负载长度
    uint16_t actualPayloadLen = len - headerSize;
    if (pkt->payloadLen != actualPayloadLen) {
        ESP_LOGE(TAG, "Payload length mismatch: expected=%d, actual=%d",
                pkt->payloadLen, actualPayloadLen);
        return;
    }

    // 检查负载长度是否合理
    if (pkt->payloadLen == 0 || pkt->payloadLen > 1024) {
        ESP_LOGE(TAG, "Invalid payload length: %d", pkt->payloadLen);
        return;
    }

    // 计算需要的总长度：FrameHead + timeRecord + 4字节头部 + Opus数据
    totalLen = sizeof(FrameHead) + sizeof(SkAudioDownlinkTimeRecord) + 4 + pkt->payloadLen;

    // 分配临时缓冲区
    frameBuffer = (uint8_t *)malloc(totalLen);
    if (frameBuffer == NULL) {
        ESP_LOGE(TAG, "Failed to allocate frame buffer");
        return;
    }

    frame = (DataFrame *)frameBuffer;

    // 构造FrameHead
    frame->frameHead.headFlag = htons(FRAME_HEAD_FLAG);
    frame->frameHead.frameType = FRAME_DPKT_TERM_AND_RELAY;
    frame->frameHead.msgType = MSG_TYPE_INVALID;
    frame->frameHead.payloadLen = htons(sizeof(SkAudioDownlinkTimeRecord) + 4 + pkt->payloadLen);
    frame->frameHead.seqID = htons(pkt->seqNum);

    // 构造时间记录
    timeRecord = (SkAudioDownlinkTimeRecord *)frame->timeRecord;
    memset(timeRecord, 0, sizeof(SkAudioDownlinkTimeRecord));
    timeRecord->seq = pkt->seqNum;
    timeRecord->len = 4 + pkt->payloadLen;  // 包含4字节头部的总长度
    timeRecord->decRxTick = SkOsGetTickCnt();

    // 构造4字节头部 
    uint8_t *audioDataPos = frameBuffer + sizeof(FrameHead) + sizeof(SkAudioDownlinkTimeRecord);
    audioDataPos[0] = pkt->seqNum & 0xFF;           // seqNum低字节
    audioDataPos[1] = (pkt->seqNum >> 8) & 0xFF;    // seqNum高字节
    audioDataPos[2] = pkt->payloadLen & 0xFF;       // payloadLen低字节
    audioDataPos[3] = (pkt->payloadLen >> 8) & 0xFF; // payloadLen高字节

    // 复制Opus音频数据（在4字节头部之后）
    memcpy(audioDataPos + 4, pkt->data, pkt->payloadLen);

    // 调用现有的音频处理函数（完全复用现有逻辑）
    RlinkPlayAudioData(ctrl, frameBuffer,
                       sizeof(SkAudioDownlinkTimeRecord) + 4 + pkt->payloadLen);
    
    // 释放临时缓冲区
    free(frameBuffer);

    return;
}
