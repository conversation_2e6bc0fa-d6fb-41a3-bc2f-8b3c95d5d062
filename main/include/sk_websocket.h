/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_websocket.h
 * @description: Websocket协议, 用于与服务器通信, 数据长度不超过1024字节.
 * @author: <PERSON>
 * @date: 2025-07-18
 */

 #ifndef SK_WEBSOCKET_H
#define SK_WEBSOCKET_H

#include <stdint.h>
#include <stdbool.h>
#include "sk_common.h"
#ifdef WS_SUPPORT_SK_BUF_POOL
#include "sk_buf_pool.h"
#endif

#define SK_WS_VERSION 0x01

#define SK_WS_FRAME_TYPE_TEXT 0x01
#define SK_WS_FRAME_TYPE_BINARY 0x02
#define SK_WS_FRAME_TYPE_CLOSE 0x08
#define SK_WS_FRAME_TYPE_PING 0x09
#define SK_WS_FRAME_TYPE_PONG 0x0A

#define SK_WS_PACKET_FLAG_BINARY    0x01

enum {
    SK_WS_DATA_TYPE_AUDIO = 0x01,
};

enum {
    SK_WS_EVENT_CONNECTED = 0x01,
    SK_WS_EVENT_DISCONNECTED = 0x02,
    SK_WS_EVENT_ERROR = 0x03,
    SK_WS_EVENT_PINGPONG = 0x04,
};

typedef struct {
    uint8_t version;        // procotol version
    uint8_t type;           // payload type
    uint16_t seqNum;        // sequence number
    uint16_t payloadLen;    // payload length
    uint16_t resv;          // reserved
    uint8_t data[0];
} SkWsBinaryHeader_t;

typedef void (*SkWsEventCallback_t)(void *arg, uint32_t event);
typedef void (*SkWsDataCallback_t)(void *arg, void *data, uint16_t len);


int32_t SkWsInit();
void SkWsDeinit();
void SkWsStart();
void SkWsStop();
void SkWsStartConnect();
void SkWsStopConnect();
#ifdef WS_SUPPORT_SK_BUF_POOL
int32_t SkWsSend(SkBufPoolNode *node, bool binary);
#endif
// 将数据打包成websocket协议数据, 输出打包数据的长度
size_t SkWsPacketData(uint8_t *out, size_t outLen, uint8_t *in, size_t inLen, uint8_t flag);
int32_t SkWsSendRaw(uint8_t *data, size_t len);
void SkWsSetServerIp(const char *ip, uint16_t port);
void SkWsRegOnBinDataCallback(SkWsDataCallback_t callback, void *arg);
void SkWsRegOnTxtDataCallback(SkWsDataCallback_t callback, void *arg);
void SkWsRegOnEventCallback(SkWsEventCallback_t callback, void *arg);
bool SkWsIsConnected();

#ifdef __cplusplus
extern "C" {
#endif

#ifdef __cplusplus
}
#endif

#endif // SK_WEBSOCKET_H