# WebSocket JSON功能说明

## 概述

本项目已成功实现WebSocket JSON消息解析功能，支持通过WebSocket接收和处理JSON格式的文本消息。

## 功能特点

✅ **JSON消息解析**：支持解析WebSocket接收的JSON文本消息  
✅ **命令处理**：支持start_record、stop_record、get_status等命令  
✅ **配置处理**：支持配置消息处理  
✅ **函数拆分**：避免使用goto，采用小函数设计，易于维护  
✅ **内存安全**：正确的内存分配和释放机制  
✅ **错误处理**：完善的错误处理和日志记录  

## 代码结构

### C代码部分

#### 1. 头文件修改 (`main/include/sk_rlink.h`)
- 添加了 `RLINK_EVENT_RX_JSON_DATA` 事件类型
- 添加了 `SkRlinkFeedWebSocketText` 函数声明

#### 2. 实现文件修改 (`main/protocol/sk_rlink.c`)
- 添加了cJSON库依赖
- 实现了以下函数：
  - `ProcessJsonCommand()`: 处理JSON命令消息
  - `ProcessJsonConfig()`: 处理JSON配置消息  
  - `ProcessJsonMessage()`: 主JSON消息处理函数
  - `SkRlinkFeedWebSocketText()`: WebSocket文本数据入口函数
- 修改了 `RlinkProcLocalMsg()` 函数，添加JSON消息处理分支

#### 3. 构建配置修改 (`main/protocol/CMakeLists.txt`)
- 添加了json库依赖

#### 4. 应用初始化修改 (`main/app/main.c`)
- 注册了WebSocket文本数据回调函数

### Python服务器部分 (`websocket_audio_server.py`)

#### 新增功能：
- **双向通信**：支持接收和发送JSON消息
- **消息处理**：处理来自客户端的JSON消息
- **广播功能**：向所有连接的客户端广播消息
- **命令行接口**：支持通过命令行发送测试消息
- **测试消息**：内置测试JSON消息生成功能

## 支持的JSON消息格式

### 1. 命令消息
```json
{
  "type": "command",
  "seq": 1001,
  "timestamp": 1640995200,
  "data": {
    "cmd": "start_record|stop_record|get_status",
    "params": {
      "format": "opus",
      "sample_rate": 16000
    }
  }
}
```

### 2. 配置消息
```json
{
  "type": "config",
  "seq": 1002,
  "timestamp": 1640995200,
  "data": {
    "server_ip": "************",
    "server_port": 8768,
    "volume": 80
  }
}
```

## 使用方法

### 1. 编译和烧录ESP32代码
```bash
cd /opt/Amor/work/sk-terminal_1
./scripts/build.sh
# 然后烧录到ESP32设备
```

### 2. 运行WebSocket服务器（用于测试）
```bash
# 运行音频服务器（支持JSON功能）
python3 websocket_audio_server.py

# 在服务器运行时，可以输入以下命令：
# test   - 发送测试JSON消息
# status - 查看服务器状态
# help   - 显示帮助
# quit   - 退出服务器
```

### 3. 运行JSON测试客户端
```bash
# 运行测试客户端向ESP32发送JSON消息
python3 test_json_client.py
```

## 测试验证

### 1. 编译验证
- ✅ 代码编译成功，无错误和警告

### 2. 功能测试
使用 `test_json_client.py` 可以测试以下功能：
- 发送start_record命令
- 发送stop_record命令  
- 发送get_status命令
- 发送配置消息
- 发送未知命令（测试错误处理）

### 3. 日志验证
ESP32设备会输出详细的日志信息：
```
I (12345) SkRlink: Processing JSON: {"type":"command",...}
I (12346) SkRlink: JSON message type: command
I (12347) SkRlink: Received command: start_record
I (12348) SkRlink: Command: Start recording
```

## 代码优化亮点

### 1. 避免goto语句
- 原代码使用goto和cleanup标签
- 重构后使用小函数分解，提高可维护性

### 2. 函数职责单一
- `ProcessJsonCommand()`: 专门处理命令消息
- `ProcessJsonConfig()`: 专门处理配置消息
- `ProcessJsonMessage()`: 协调整体处理流程

### 3. 内存管理
- 正确的cJSON对象释放
- 动态分配内存的及时释放
- 避免内存泄漏

### 4. 错误处理
- 每个函数都有明确的返回值
- 详细的错误日志记录
- 优雅的错误恢复机制

## 扩展建议

1. **添加更多命令类型**：可以在 `ProcessJsonCommand()` 中添加更多命令处理
2. **实现JSON响应生成**：添加向服务器发送JSON响应的功能
3. **添加参数验证**：对JSON消息的参数进行更严格的验证
4. **性能优化**：对于高频消息，可以考虑使用消息池等优化手段

## 总结

本次实现成功地为ESP32项目添加了完整的JSON消息处理能力，代码结构清晰，易于维护和扩展。通过函数拆分避免了goto语句的使用，提高了代码质量。
