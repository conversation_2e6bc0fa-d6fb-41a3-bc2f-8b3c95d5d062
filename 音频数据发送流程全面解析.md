# 音频数据发送流程全面解析

## 🎯 **系统架构概览**

sk-terminal是一个基于ESP32的智能音频终端，实现了完整的音频采集、处理、编码和网络传输功能。

### **核心模块组成**
```
[硬件音频设备] → [音频采集] → [语音处理] → [Opus编码] → [网络传输]
     ↓              ↓           ↓            ↓           ↓
  ES8311/ES7210   SkRecorder   SkSr/AFE   SkOpusEnc   SkRlink
```

## 📊 **完整数据流程图**

```mermaid
graph TD
    A[麦克风硬件] --> B[AudioDevRead]
    B --> C[SkRecorderTask]
    C --> D[SkAudioProcessFeed]
    D --> E[SkSrDataIn]
    D --> F[SkDataToEncode]
    F --> G[SkOpusEncEnqueue]
    G --> H[SkOpusEncProcAudioBuf]
    H --> I[SkRlinkFeedReordAudio]
    I --> J[RlinkSendAudioFrame]
    J --> K[TCP网络传输]
    
    E --> L[语音识别AFE]
    L --> M[唤醒词检测]
    L --> N[命令识别]
```

## 🔧 **详细流程分析**

### **阶段1: 硬件音频采集**

#### **1.1 音频设备初始化**
```c
// main/app/main.c:150
SkAudioInit(sizeof(uint16_t), 960);

// main/audio/sk_afe.c:159
void SkAudioInit(int32_t bytesPerSample, int32_t playerSamplePerChunk) {
    // 初始化语音识别模型
    g_models = esp_srmodel_init("model");
    
    // 初始化AFE (Audio Front End)
    chunkSize = SkAudioInitSrAfe();
    
    // 初始化各个音频模块
    SkSrInit(chunkSize, recChannelNum, bytesPerSample, afeChannelNum);
    SkPlayerInit(bytesPerSample, playerSamplePerChunk);
    SkRecorderInit(bytesPerSample, chunkSize, recChannelNum);
    
    // 启动音频处理任务
    SkAudioStartTasks();
}
```

#### **1.2 录音器初始化**
```c
// main/audio/sk_recorder.c:42
uint32_t SkRecorderInit(int32_t bytesPerSample, int32_t samplePerChunk, int32_t channelNum) {
    SkRecorderCtrl *ctrl = &g_recorderCtrl;
    int bufferSize = samplePerChunk * bytesPerSample * channelNum;
    
    // 分配音频缓冲区
    ctrl->buff = malloc(bufferSize);
    ctrl->bufferSize = bufferSize;
    ctrl->samplePerChunk = samplePerChunk;
    ctrl->channelNum = channelNum;
    
    return SK_RET_SUCCESS;
}
```

#### **1.3 音频设备配置**
```c
// main/board/audio_8311_7210.c:72
int32_t AudioDevInit(i2c_master_bus_handle_t i2cBus, uint32_t sampleRate, int bitsPerChan) {
    SkAudioDev *audioDev = &g_audioDev;
    
    // 配置音频参数
    AudioDevConfig(audioDev, i2cBus, sampleRate, sampleRate);
    
    // 初始化I2S通道
    AudioDevInitI2SChannel(audioDev);
    AudioDevInitSpkI2S(audioDev);  // 扬声器
    AudioDevInitMicI2S(audioDev);  // 麦克风
    
    // 初始化编解码器
    AudioDevInitSpk(audioDev);     // ES8311 DAC
    AudioDevInitMic(audioDev);     // ES7210 ADC
    
    return SK_RET_SUCCESS;
}
```

### **阶段2: 音频数据采集循环**

#### **2.1 录音任务主循环**
```c
// main/audio/sk_recorder.c:96
void SkRecorderTask(void *arg) {
    SkRecorderCtrl *ctrl = &g_recorderCtrl;
    
    while (ctrl->taskFlag) {
        // 等待录音事件
        xEventGroupWaitBits(ctrl->eventGroup, RECORDER_EVENT_RUN, 
                           pdFALSE, pdFALSE, portMAX_DELAY);
        
        // 从硬件读取音频数据
        SkBspReadAudio(ctrl->buff, ctrl->bufferSize);
        
        // 处理音频数据
        SkAudioProcessFeed(ctrl->buff, ctrl->samplePerChunk, ctrl->channelNum);
    }
}
```

#### **2.2 硬件音频读取**
```c
// main/board/sk_board_aec.c:88
sk_err_t SkBspReadAudio(int16_t *buffer, int bufferLen) {
#ifdef CONFIG_NONCODEC_DEV
    return AudioDevReadNoncodec(buffer, bufferLen);
#else
    return AudioDevRead(buffer, bufferLen);  // 调用ES7210读取
#endif
}

// main/board/audio_8311_7210.c:225
int AudioDevRead(int16_t* dest, int bufSize) {
    SkAudioDev *audioDev = &g_audioDev;
    if (audioDev->micEnabled) {
        // 从ES7210 ADC读取音频数据
        esp_codec_dev_read(audioDev->micDev, (void*)dest, bufSize);
    }
    return bufSize;
}
```

### **阶段3: 音频数据处理**

#### **3.1 音频处理入口**
```c
// main/audio/sk_sr.c:488
void SkAudioProcessFeed(int16_t *buffer, int sampleCnt, int channelNum) {
    SkSrCtrl *ctrl = &g_skSrCtrl;
    
    // 调试：发送原始PCM数据
    SkDfxLinkSendAudio(MSG_DFX_AND_TERM_PCM2, (uint8_t *)buffer, 
                       sampleCnt * sizeof(int16_t) * channelNum);
    
    // 数据输入到语音识别缓冲区
    SkSrDataIn(buffer, sampleCnt);
    
    // AFE处理（回声消除、降噪等）
    SkVcAfeFeed(ctrl);   // 语音通信AFE
    SkSrAfeFeed(ctrl);   // 语音识别AFE
    
    // 编码处理（如果未启用VC AFE）
#ifndef CONFIG_VC_AFE_ENABLED
    SkDataToEncode(buffer, sampleCnt);
#endif
}
```

#### **3.2 语音识别数据输入**
```c
// main/audio/sk_sr.c:351
void SkSrDataIn(int16_t *buffer, int sampleCnt) {
    SkSrCtrl *ctrl = &g_skSrCtrl;
    int wPos = ctrl->bufferWritePos;
    
    // 数据格式转换和缓冲
    for (int i = 0; i < sampleCnt; i++) {
#ifdef CONFIG_NONCODEC_DEV
        ctrl->srBuffer[2 * wPos] = buffer[i] << 5;  // 单声道
#else
        ctrl->srBuffer[2 * wPos] = buffer[2 * i] << 2;      // 左声道
        ctrl->srBuffer[2 * wPos + 1] = buffer[2 * i + 1];   // 右声道
#endif
        wPos++;
    }
    ctrl->bufferWritePos += sampleCnt;
}
```

#### **3.3 编码数据准备**
```c
// main/audio/sk_sr.c:384
void SkDataToEncode(uint16_t *buffer, int sampleCnt) {
    SkSrCtrl *ctrl = &g_skSrCtrl;
    
    // 数据格式调整
    for (int i = 0; i < sampleCnt; i++) {
#ifdef CONFIG_NONCODEC_DEV
        buffer[i] = buffer[i] << 5;
#else
        buffer[i] = buffer[2*i] << 2;  // 只取左声道
#endif
    }
    
    // 检查按键状态决定是否发送
    if (SkBspReadUserInput() == SK_KEY_PRESS_UP) {
        // 发送音频数据到编码器
        SkSrSendSpeechData(ctrl, buffer, sampleCnt * sizeof(int16_t), 
                          SK_ENC_DATA_FLAG_VAD);
    }
}
```

### **阶段4: Opus编码处理**

#### **4.1 编码器数据入队**
```c
// main/opus_coder/sk_opus_enc.c:336
void SkOpusEncEnqueue(uint16_t *buff, size_t len, uint32_t dataMode, uint32_t tickCnt) {
    SkOpusEncCtrl *ctrl = &g_opusEncCtrl;
    
    if (ctrl->encodeFlag == 0) return;
    
    // 获取空闲音频缓冲区
    SkAudioBuf *audioBuf = SkAudioBufferGetFree(ctrl->inQueue, 0);
    if (audioBuf == NULL) return;
    
    // 复制音频数据
    uint16_t *codeBuf = (uint16_t *)audioBuf->data;
    memcpy(codeBuf, buff, len);
    audioBuf->length = len;
    audioBuf->speechFlag = (uint16_t)dataMode;
    audioBuf->timeRecord[0] = tickCnt;
    
    // 发送到编码器消息队列
    SkOpusMsg msg = {
        .event = SK_ENC_EVENT_PCM,
        .arg = audioBuf,
        .timestamp = tickCnt
    };
    xQueueSend(ctrl->msgQueue, &msg, portMAX_DELAY);
    ctrl->enQueueFrameCnt++;
}
```

#### **4.2 编码器数据处理**
```c
// main/opus_coder/sk_opus_enc.c:201
void SkOpusEncProcAudioBuf(SkOpusEncHandler handler, SkAudioBuf *inBuf) {
    SkOpusEncCtrl *ctrl = (SkOpusEncCtrl *)handler;
    uint16_t pktSize;
    uint8_t *pktData = NULL;
    uint32_t currTick = SkOsGetTickCnt();
    
    // 检查缓冲区超时
    uint32_t timestamp = inBuf->timeRecord[0];
    if ((currTick - timestamp) > 100) {
        ESP_LOGI(TAG, "Audio buffer timeout, %u->%u tick", timestamp, currTick);
    }
    
    // Opus编码处理
    if (ctrl->localEndFlag == 1) {
        // 实时音频编码
        pktSize = SkOpusEncData(ctrl, inBuf);
        if (pktSize == 0) return;
        pktData = ctrl->opusBuf;
    } else {
        // 本地音频播放
        SkAudioBufferPutFree(ctrl->inQueue, inBuf);
        pktData = SkOpusEncLocalGetPkt(ctrl, &pktSize);
        if (pktData == NULL) return;
    }
    
    // 调用回调函数发送编码数据
    if (ctrl->cb != NULL) {
        ctrl->cb(ctrl->cbArg, pktData, pktSize, currTick);
    }
}
```

### **阶段5: 网络传输**

#### **5.1 音频数据输入到传输层**
```c
// main/protocol/sk_rlink.c:266
void SkRlinkFeedReordAudio(void *handler, const uint8_t *data, size_t len, uint32_t timestamp) {
    RlinkCtrlInfo *ctrl = (RlinkCtrlInfo *)handler;
    
    if (ctrl->linkFlag != RLINK_LINK_RUN) return;
    
    // 获取音频缓冲区
    SkAudioBuf *audioBuf = SkAudioBufferGetFree(ctrl->recordQueue, 0);
    if (audioBuf == NULL) {
        ESP_LOGE(TAG, "Failed to get audio buffer");
        return;
    }
    
    // 复制Opus编码数据
    memcpy((int8_t *)&audioBuf->data[audioBuf->offset + 32], data, len);
    audioBuf->length = len;
    
    // 发送到传输队列
    if (SkRlinkSendAudioData(audioBuf, timestamp) != SK_RET_SUCCESS) {
        ESP_LOGE(TAG, "SkRlinkSendAudioData failed!");
        SkAudioBufferPutFree(ctrl->recordQueue, audioBuf);
    }
}
```

#### **5.2 音频帧网络发送**
```c
// main/protocol/sk_rlink.c:169
int32_t RlinkSendAudioFrame(RlinkCtrlInfo *ctrl, DataFrame *frame, 
                           uint32_t dataLen, uint32_t timestamp) {
    // 获取序列号
    uint16_t seq = RlinkGetTxSeqId(ctrl);
    
    // 设置时间记录
    SkAudioUplinkTimeRecord *timeRecord = (SkAudioUplinkTimeRecord *)frame->timeRecord;
    timeRecord->seq = seq;
    timeRecord->len = dataLen;
    timeRecord->encDoneTick = timestamp;
    timeRecord->encTxTick = SkOsGetTickCnt();
    
    // 编码数据帧
    EncodeRelayDataMsg(frame, dataLen + sizeof(frame->timeRecord));
    frame->frameHead.seqID = htons(seq);
    
    // TCP发送
    int ret = RlinkSendRemoteMsg(ctrl, frame, 
                                dataLen + sizeof(FrameHead) + sizeof(frame->timeRecord));
    
    // 错误处理
    if (ret != SK_RET_SUCCESS) {
        static int failCnt = 0;
        failCnt++;
        if (failCnt >= 8) {
            ctrl->linkFlag = RLINK_LINK_STOP;  // 连续失败则停止链路
        }
    }
    
    return ret;
}
```

## 🔗 **关键回调链路配置**

### **初始化时的回调设置**
```c
// main/app/main.c:153-154
SkSrSetSendFunc(SkOpusEncEnqueue);           // 语音识别 → Opus编码
SkOpusEncSetCallback(SkRlinkFeedReordAudio); // Opus编码 → 网络传输
```

### **完整回调链**
```
音频采集 → SkAudioProcessFeed()
    ↓
语音处理 → SkSrSendSpeechData()
    ↓
编码入队 → SkOpusEncEnqueue()  [回调1]
    ↓
编码处理 → SkOpusEncProcAudioBuf()
    ↓
网络传输 → SkRlinkFeedReordAudio()  [回调2]
    ↓
TCP发送 → RlinkSendAudioFrame()
```

## 📈 **性能特征**

### **数据流量统计**
- **采样率**: 16kHz
- **位深**: 16bit
- **声道**: 双声道输入，单声道编码
- **帧长**: 60ms (960 samples)
- **原始数据**: 1920 bytes/frame
- **Opus编码**: ~20-60 bytes/frame (压缩比约30:1)

### **延迟分析**
- **采集延迟**: ~60ms (一帧时间)
- **处理延迟**: ~10ms (AFE + 编码)
- **网络延迟**: 取决于网络条件
- **总延迟**: ~70ms + 网络延迟

### **缓冲区管理**
- **录音缓冲区**: 1920 bytes
- **编码队列**: 4个缓冲区
- **传输队列**: 4个缓冲区
- **总内存占用**: ~20KB

## 🎯 **总结**

sk-terminal实现了一个高效的音频处理管道：

1. **硬件层**: ES7210 ADC采集 → I2S传输
2. **驱动层**: AudioDev读取 → 格式转换
3. **处理层**: AFE处理 → 语音识别
4. **编码层**: Opus压缩 → 数据打包
5. **传输层**: TCP发送 → 网络传输

整个系统通过回调机制实现了高效的数据流转，同时保持了模块间的解耦。
