# 状态机代码调度任务和运行程序全面分析

## 🎯 **状态机架构概览**

sk-terminal采用分层状态机架构，通过事件驱动的方式管理整个系统的运行状态和任务调度。

### **核心架构组成**
```
[主状态机] → [子状态机] → [具体业务逻辑]
     ↓           ↓              ↓
  SkSmMain   SkSmItem[]    业务处理函数
```

## 📊 **状态机结构图**

```mermaid
graph TD
    A[系统启动] --> B[SkSmInit初始化]
    B --> C[创建消息队列]
    B --> D[创建定时器]
    B --> E[创建状态机任务]
    B --> F[初始化子状态]
    
    E --> G[SkSmMain主循环]
    D --> H[SmTimerCallback定时器]
    H --> I[发送TICK事件]
    I --> G
    
    G --> J{事件类型判断}
    J -->|SM_EVENT_CMD| K[命令处理]
    J -->|SM_EVENT_LINK| L[链路事件]
    J -->|SM_EVENT_NETWORK| M[网络事件]
    J -->|SM_EVENT_SYSTEM| N[系统事件]
    
    K --> O[SkSmTopCmdProc]
    L --> P[SkSmLinkEventProc]
    M --> Q[SmOnNetworkEvent]
    N --> R[SkSmSystemEventProc]
    
    O --> S[子状态处理]
    P --> S
    Q --> S
    R --> S
    
    S --> T[smItem->eventProc]
    T --> G
```

## 🔧 **详细组件分析**

### **1. 状态机控制结构**

#### **1.1 核心控制结构SkStateCtrl**
```c
// main/app/sm_top.c:37
typedef struct {
    int32_t state;                  // 当前状态
    uint8_t clinkState;             // Clink连接状态
    uint8_t rlinkState;             // Rlink连接状态
    int32_t netState;               // 网络状态
    
    uint32_t secTime;               // 秒级计数器
    int32_t runFlag;                // 状态机运行标志
    uint32_t cmdMask;               // 命令掩码（权限控制）
    uint32_t waitWorkId;            // 等待的工作ID
    
    esp_timer_handle_t timer;       // 1秒定时器
    QueueHandle_t msgQueue;         // 消息队列
    SkSmItem smItemList[STATE_MAX]; // 子状态列表
    SkSpeechCmdProc *cmdProcMap;    // 命令处理映射表
    TaskHandle_t taskHandle;        // 状态机任务句柄
} SkStateCtrl;
```

#### **1.2 状态定义**
```c
// main/app/sm.h:14
enum {
    STATE_INIT = 0,         // 初始化状态
    STATE_CONNECTING = 1,   // 网络连接中
    STATE_OTA = 2,          // OTA升级
    STATE_IDLE = 3,         // 空闲状态
    STATE_ERROR = 4,        // 错误状态
    STATE_CHAT = 5,         // 聊天状态
    STATE_CALL = 6,         // 通话状态
    STATE_MUSIC = 7,        // 音乐播放
    STATE_CONFIG = 8,       // 配置状态
    STATE_QUERY = 9,        // 查询状态
    STATE_HELP = 10,        // 帮助状态
    STATE_REBOOT = 11,      // 重启状态
    STATE_PM = 12,          // 省电模式
    STATE_MAX,
};
```

### **2. 状态机初始化流程**

#### **2.1 SkSmInit初始化**
```c
// main/app/sm_top.c:657
SkStateHandler SkSmInit() {
    SkStateCtrl *ctrl = &g_topStateCtrl;
    
    // 1. 创建消息队列（32个事件缓冲）
    ctrl->msgQueue = xQueueCreate(32, sizeof(SkSmEvent));
    
    // 2. 设置命令处理映射表
    ctrl->cmdProcMap = g_cmdProcMap;
    ctrl->cmdProcMapLen = sizeof(g_cmdProcMap) / sizeof(SkSpeechCmdProc);
    
    // 3. 初始化状态和标志
    ctrl->state = STATE_IDLE;
    ctrl->runFlag = 1;
    ctrl->cmdMask = LOCAL_CMD_MASK;  // 初始只允许本地命令
    
    // 4. 初始化所有子状态
    SkSmIdleInit(&ctrl->smItemList[STATE_IDLE], NULL, (SkStateHandler)ctrl);
    SkSmCallInit(&ctrl->smItemList[STATE_CALL], SmStateEndCallback, (SkStateHandler)ctrl);
    SkSmMusicInit(&ctrl->smItemList[STATE_MUSIC], SmStateEndCallback, (SkStateHandler)ctrl);
    // ... 其他状态初始化
    
    // 5. 创建1秒定时器
    ESP_ERROR_CHECK(esp_timer_create(&timerArgs, &ctrl->timer));
    ESP_ERROR_CHECK(esp_timer_start_periodic(ctrl->timer, 1000 * 1000)); // 1秒周期
    
    // 6. 创建状态机主任务
    xTaskCreate(SkSmMain, "SkStateMachine", 8192, (void*)&g_topStateCtrl, 5, &g_topStateCtrl.taskHandle);
    
    return (SkStateHandler)ctrl;
}
```

### **3. 状态机主循环**

#### **3.1 SkSmMain主任务**
```c
// main/app/sm_top.c:588
void SkSmMain(void *arg) {
    SkStateCtrl *ctrl = (SkStateCtrl *)arg;
    SkSmEvent event;
    bool continueProc;
    
    while (ctrl->runFlag != 0) {
        // 1. 从消息队列接收事件（阻塞等待）
        ret = xQueueReceive(ctrl->msgQueue, &event, portMAX_DELAY);
        if (ret != pdPASS) continue;
        
        // 2. 事件分类处理
        if (event.event == SM_EVENT_CMD) {
            continueProc = SkSmTopCmdProc(ctrl, event.subEvent);        // 命令处理
        } else if (event.event == SM_EVENT_LINK) {
            continueProc = SkSmLinkEventProc(ctrl, &event);             // 链路事件
        } else if (event.event == SM_EVENT_NETWORK) {
            continueProc = SmOnNetworkEvent(ctrl, event.subEvent);      // 网络事件
        } else if (event.event == SM_EVENT_SYSTEM) {
            continueProc = SkSmSystemEventProc(ctrl, &event);           // 系统事件
        } else {
            continueProc = false;
        }
        
        // 3. 如果需要继续处理，传递给当前子状态
        if (continueProc) {
            SkSmItem *smItem = &ctrl->smItemList[ctrl->state];
            if (smItem->eventProc != NULL) {
                smItem->eventProc(&smItem->info, &event);
            }
        }
    }
}
```

### **4. 事件处理机制**

#### **4.1 事件类型定义**
```c
// main/include/sk_sm.h:15
enum {
    SM_EVENT_CMD = 1,       // 语音命令事件
    SM_EVENT_LINK = 2,      // 链路连接事件
    SM_EVENT_NETWORK = 3,   // 网络状态事件
    SM_EVENT_SYSTEM = 4,    // 系统事件
};
```

#### **4.2 命令处理流程**
```c
// main/app/sm_top.c:305
bool SkSmTopCmdProc(SkStateCtrl *ctrl, int32_t cmd) {
    // 1. 检查是否有工作在等待完成
    if (ctrl->waitWorkId != 0) {
        SK_LOGE(TAG, "Wait work %d", ctrl->waitWorkId);
        return false;
    }
    
    // 2. 检查命令是否被允许（权限控制）
    if ((ctrl->cmdMask & (1UL << cmd)) == 0UL) {
        SK_LOGE(TAG, "Command %d not allowed", cmd);
        return true;
    }
    
    // 3. 执行命令处理函数
    SkSpeechCmdProc cmdProc = ctrl->cmdProcMap[cmd];
    if (cmdProc != NULL) {
        cmdProc(SM_EVENT_CMD, cmd, 0, 0);
        return false;
    }
    return true;
}
```

#### **4.3 命令映射表**
```c
// main/app/sm_top.c:202
SkSpeechCmdProc g_cmdProcMap[] = {
    NULL,                   // 0
    SkSmStateChange,        // SPEECH_CMD_EVENT_CHAT - 聊天
    SkSmStateChange,        // SPEECH_CMD_EVENT_CALL - 通话
    SkSmStateChange,        // SPEECH_CMD_EVENT_MUSIC - 音乐
    SkSmStateChange,        // SPEECH_CMD_EVENT_CONFIG - 配置
    SkSmStateChange,        // SPEECH_CMD_EVENT_QUERY - 查询
    SkSmCmdVolUp,           // SPEECH_CMD_EVENT_VOLUP - 音量+
    SkSmCmdVolDown,         // SPEECH_CMD_EVENT_VOLDOWN - 音量-
    SkSmCmdDbgOn,           // SPEECH_CMD_EVENT_HELP - 帮助
    // ... 更多命令映射
};
```

### **5. 状态切换机制**

#### **5.1 状态切换函数**
```c
// main/app/sm_top.c:258
void SkSmEnterNewState(SkStateCtrl *ctrl, int32_t state, int32_t event, int32_t subEvent, int32_t param1) {
    SkSmEvent smEvent;
    SkSmItem *smItem = NULL;
    
    // 1. 设置新状态
    ctrl->state = state;
    SK_LOGI(TAG, "State start %d", ctrl->state);
    
    // 2. 调用新状态的启动函数
    smItem = &ctrl->smItemList[ctrl->state];
    if (smItem->startProc != NULL) {
        smEvent.event = event;
        smEvent.subEvent = subEvent;
        smEvent.param1 = param1;
        smEvent.param2 = 0;
        smItem->startProc(&smItem->info, &smEvent);
    }
}
```

#### **5.2 停止前一个状态**
```c
// main/app/sm_top.c:276
void SkSmStopPrev(SkStateCtrl *ctrl) {
    if (ctrl->state != STATE_IDLE) {
        SK_LOGI(TAG, "State stop %d", ctrl->state);
        SkSmItem *smItem = &ctrl->smItemList[ctrl->state];
        if (smItem->stopProc != NULL) {
            smItem->stopProc(&smItem->info);
        }
        ctrl->state = STATE_IDLE;
        SkRledSetEvent(SK_LED_EVENT_IDLE);  // 设置LED状态
    }
}
```

### **6. 子状态机实现**

#### **6.1 子状态结构**
```c
// main/app/sm.h:68
typedef struct {
    SkSubStateInfo info;            // 子状态信息
    SkSmStartProc startProc;        // 启动处理函数
    SkSmStopProc stopProc;          // 停止处理函数
    SkSmEventProc eventProc;        // 事件处理函数
} SkSmItem;
```

#### **6.2 IDLE状态实现示例**
```c
// main/app/sm_top.c:733
int32_t SkSmIdleStart(SkSubStateInfo *info, const SkSmEvent *event) {
    SmIdleCtrl *ctrl = (SmIdleCtrl*)info->privateData;
    ctrl->idleTime = 0;
    SK_LOGI(TAG, "Idle state start.");
    
    // 启动录音和语音识别
    SkRecorderResume();
    SkSrProcessEnable(true);
    SkVcProcessEnable(false);
    SkRledSetEvent(SK_LED_EVENT_IDLE);
    return SK_RET_SUCCESS;
}

int32_t SkSmIdleEventProc(SkSubStateInfo *info, const SkSmEvent *event) {
    SmIdleCtrl *ctrl = (SmIdleCtrl*)info->privateData;
    if (event->event == SM_EVENT_SYSTEM && event->subEvent == SM_EVENT_SYSTEM_TICK) {
        ctrl->idleTime++;
        
        // 检查OTA更新
        SkOtaManagerOnIdleTick(ctrl->idleTime);
        
        // 20秒后检查传感器状态
        if (ctrl->idleTime == 20) {
            SK_LOGI(TAG, "Idle time %d, sensor state %d", ctrl->idleTime, SkSensorState());
        }
    }
    return SK_RET_SUCCESS;
}
```

### **7. 定时器和事件驱动**

#### **7.1 定时器回调**
```c
// main/app/sm_top.c:632
void SmTimerCallback(void* arg) {
    // 每秒发送一次TICK事件
    SkSmSendEvent((SkStateHandler)arg, SM_EVENT_SYSTEM, SM_EVENT_SYSTEM_TICK, 0, 0);
}
```

#### **7.2 事件发送机制**
```c
// main/app/sm_top.c:692
void SkSmSendEvent(SkStateHandler handler, int32_t event, int32_t subEvent, uint32_t param1, uint32_t param2) {
    SkStateCtrl *ctrl = (SkStateCtrl*)handler;
    SkSmEvent smEvent;
    
    smEvent.event = event;
    smEvent.subEvent = subEvent;
    smEvent.param1 = param1;
    smEvent.param2 = param2;
    
    // 发送到消息队列
    xQueueSend(ctrl->msgQueue, &smEvent, portMAX_DELAY);
}
```

### **8. 系统启动流程**

#### **8.1 完整启动序列**
```c
// 系统启动时的状态机流程
1. app_main() 调用 SkSmInit()
2. SkSmInit() 初始化状态机，创建任务和定时器
3. 发送 SM_EVENT_SYSTEM_INIT_OK 事件
4. SkSmSystemEventProc() 处理初始化完成事件：
   - 播放启动提示音
   - 启动WiFi连接
   - 切换到 STATE_CONNECTING 状态
   - 启动录音器
5. 进入主循环，等待各种事件
```

#### **8.2 网络连接流程**
```c
// main/app/sm_top.c:552
int32_t SkSmConnectingStateProc(SkSubStateInfo *info, const SkSmEvent *event) {
    SkStateCtrl *ctrl = (SkStateCtrl *)info->privateData;
    
    if (event->event == SM_EVENT_SYSTEM && event->subEvent == SM_EVENT_SYSTEM_TICK) {
        if (SkSmGetNetState() == NETWORK_STATE_STA_CONNECTED) {
            if (ctrl->otaFlag == 0) {
                SkSmEnterNewState(ctrl, STATE_OTA, 0, 0, 0);      // 先进行OTA检查
            } else {
                SkClinkSetFunFlag(CLINK_RUN_FLAG_START);
                SkSmEnterNewState(ctrl, STATE_IDLE, 0, 0, 0);     // 进入空闲状态
            }
            return SK_RET_SUCCESS;
        }
        
        // 60秒超时处理
        ctrl->timeCnt++;
        if (ctrl->timeCnt == 60) {
            // 播放需要配置提示音
            uint8_t audioList[2] = {AUDIO_IDX_NEED_CFG};
            SkSmPlayLocalNotice(audioList, 1, true);
        }
    }
    return SK_RET_SUCCESS;
}
```

## 🎯 **任务调度总结**

### **调度机制特点**
1. **事件驱动**: 基于FreeRTOS消息队列的事件驱动架构
2. **分层处理**: 主状态机 + 子状态机的分层处理模式
3. **权限控制**: 通过cmdMask实现命令权限管理
4. **定时调度**: 1秒定时器提供周期性事件
5. **异步处理**: 非阻塞的事件处理机制

### **运行流程**
```
系统启动 → 状态机初始化 → 网络连接 → OTA检查 → 空闲状态 → 响应用户命令 → 状态切换 → 业务处理
```

### **核心优势**
- **模块化**: 每个状态独立实现，便于维护
- **可扩展**: 易于添加新状态和命令
- **实时性**: 基于FreeRTOS的实时调度
- **稳定性**: 完善的错误处理和状态恢复机制

这个状态机系统为sk-terminal提供了强大而灵活的任务调度和程序运行框架。
