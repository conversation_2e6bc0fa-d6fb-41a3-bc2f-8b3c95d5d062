#!/usr/bin/env python3
"""
WebSocket JSON测试客户端
用于测试ESP32的JSON消息处理功能
"""

import asyncio
import websockets
import json
import time

async def test_json_client():
    uri = "ws://************:8768"  # 与服务器配置一致
    
    try:
        async with websockets.connect(uri) as websocket:
            print(f"[{time.strftime('%H:%M:%S')}] 连接到服务器: {uri}")
            
            # 测试命令消息
            command_msg = {
                "type": "command",
                "seq": 1001,
                "timestamp": int(time.time()),
                "data": {
                    "cmd": "start_record",
                    "params": {
                        "format": "opus",
                        "sample_rate": 16000
                    }
                }
            }
            
            print(f"[{time.strftime('%H:%M:%S')}] 发送命令消息...")
            await websocket.send(json.dumps(command_msg))
            
            # 等待响应
            response = await websocket.recv()
            if isinstance(response, str):
                print(f"[{time.strftime('%H:%M:%S')}] 收到响应: {response}")
            
            await asyncio.sleep(1)
            
            # 测试状态查询
            status_msg = {
                "type": "status",
                "seq": 1002,
                "timestamp": int(time.time()),
                "data": {
                    "query": "device_info"
                }
            }
            
            print(f"[{time.strftime('%H:%M:%S')}] 发送状态查询...")
            await websocket.send(json.dumps(status_msg))
            
            # 等待响应
            response = await websocket.recv()
            if isinstance(response, str):
                print(f"[{time.strftime('%H:%M:%S')}] 收到响应: {response}")
            
            await asyncio.sleep(1)
            
            # 测试配置消息
            config_msg = {
                "type": "config",
                "seq": 1003,
                "timestamp": int(time.time()),
                "data": {
                    "server_ip": "************",
                    "server_port": 8768,
                    "volume": 80
                }
            }
            
            print(f"[{time.strftime('%H:%M:%S')}] 发送配置消息...")
            await websocket.send(json.dumps(config_msg))
            
            # 等待响应
            response = await websocket.recv()
            if isinstance(response, str):
                print(f"[{time.strftime('%H:%M:%S')}] 收到响应: {response}")
            
            print(f"[{time.strftime('%H:%M:%S')}] 测试完成")
            
    except Exception as e:
        print(f"[{time.strftime('%H:%M:%S')}] 连接错误: {e}")

if __name__ == "__main__":
    print("WebSocket JSON测试客户端")
    print("=" * 40)
    asyncio.run(test_json_client())
