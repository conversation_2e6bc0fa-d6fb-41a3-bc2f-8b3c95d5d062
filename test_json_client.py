#!/usr/bin/env python3
"""
WebSocket JSON测试客户端
用于测试ESP32的JSON消息处理功能
"""

import asyncio
import websockets
import json
import time

async def test_json_client():
    uri = "ws://************:8768"  # 与ESP32配置一致

    try:
        async with websockets.connect(uri) as websocket:
            print(f"[{time.strftime('%H:%M:%S')}] 连接到ESP32: {uri}")

            # 测试命令消息
            command_msg = {
                "type": "command",
                "seq": 1001,
                "timestamp": int(time.time()),
                "data": {
                    "cmd": "start_record",
                    "params": {
                        "format": "opus",
                        "sample_rate": 16000
                    }
                }
            }

            print(f"[{time.strftime('%H:%M:%S')}] 发送命令消息: start_record")
            await websocket.send(json.dumps(command_msg))

            await asyncio.sleep(2)

            # 测试停止录音命令
            stop_msg = {
                "type": "command",
                "seq": 1002,
                "timestamp": int(time.time()),
                "data": {
                    "cmd": "stop_record"
                }
            }

            print(f"[{time.strftime('%H:%M:%S')}] 发送命令消息: stop_record")
            await websocket.send(json.dumps(stop_msg))

            await asyncio.sleep(2)

            # 测试获取状态命令
            status_msg = {
                "type": "command",
                "seq": 1003,
                "timestamp": int(time.time()),
                "data": {
                    "cmd": "get_status"
                }
            }

            print(f"[{time.strftime('%H:%M:%S')}] 发送命令消息: get_status")
            await websocket.send(json.dumps(status_msg))

            await asyncio.sleep(2)

            # 测试配置消息
            config_msg = {
                "type": "config",
                "seq": 1004,
                "timestamp": int(time.time()),
                "data": {
                    "server_ip": "************",
                    "server_port": 8768,
                    "volume": 80
                }
            }

            print(f"[{time.strftime('%H:%M:%S')}] 发送配置消息")
            await websocket.send(json.dumps(config_msg))

            await asyncio.sleep(2)

            # 测试未知命令
            unknown_msg = {
                "type": "command",
                "seq": 1005,
                "timestamp": int(time.time()),
                "data": {
                    "cmd": "unknown_command"
                }
            }

            print(f"[{time.strftime('%H:%M:%S')}] 发送未知命令消息")
            await websocket.send(json.dumps(unknown_msg))

            print(f"[{time.strftime('%H:%M:%S')}] 测试完成，请查看ESP32日志")

    except Exception as e:
        print(f"[{time.strftime('%H:%M:%S')}] 连接错误: {e}")

if __name__ == "__main__":
    print("WebSocket JSON测试客户端")
    print("=" * 40)
    print("此客户端将向ESP32发送各种JSON测试消息")
    print("请确保ESP32已连接并运行WebSocket服务器")
    print()
    asyncio.run(test_json_client())
