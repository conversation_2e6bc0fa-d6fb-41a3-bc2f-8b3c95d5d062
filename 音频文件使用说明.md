# WebSocket音频服务器 - 音频文件使用说明

## 概述

WebSocket音频服务器现在支持自动加载和播放音频文件，解决了之前一连接就自动发送音频流的问题。

## 功能特点

✅ **自动音频文件加载**：自动扫描audio文件夹中的WAV文件  
✅ **按需播放**：只有在收到start_audio命令时才开始发送音频流  
✅ **动态重载**：支持运行时重新加载音频文件  
✅ **多文件支持**：支持播放多个音频文件，循环播放  
✅ **命令控制**：通过JSON命令或服务器命令行控制播放  

## 文件夹结构

```
sk-terminal_1/
├── websocket_audio_server.py
├── test_json_client.py
├── audio/                    # 音频文件夹
│   ├── sample1.wav          # 音频文件1
│   ├── sample2.wav          # 音频文件2
│   └── ...                  # 更多音频文件
└── ...
```

## 支持的音频格式

- **WAV文件**：.wav, .WAV
- **采样率**：建议16kHz（与ESP32音频处理匹配）
- **声道**：单声道或立体声
- **位深度**：16位

## 使用方法

### 1. 准备音频文件
将您的WAV音频文件放入`audio`文件夹中：
```bash
# Windows下
mkdir audio
copy your_audio_files.wav audio\

# 或者直接在文件管理器中创建audio文件夹并复制文件
```

### 2. 启动服务器
```bash
python websocket_audio_server.py
```

服务器启动时会自动扫描audio文件夹：
```
在 'audio' 文件夹中找到 2 个音频文件:
  - audio\sample1.wav
  - audio\sample2.wav
已加载 2 个音频文件，准备播放
```

### 3. 控制音频播放

#### 通过服务器命令行：
- `start` - 启动音频流
- `stop` - 停止音频流  
- `reload` - 重新加载audio文件夹中的音频文件
- `status` - 查看当前状态

#### 通过JSON命令（从ESP32或测试客户端）：
```json
// 启动音频流
{
  "type": "command",
  "seq": 1001,
  "data": {
    "cmd": "start_audio"
  }
}

// 停止音频流
{
  "type": "command", 
  "seq": 1002,
  "data": {
    "cmd": "stop_audio"
  }
}

// 重新加载音频文件
{
  "type": "command",
  "seq": 1003, 
  "data": {
    "cmd": "reload_audio"
  }
}
```

## 工作流程

1. **服务器启动**：自动扫描audio文件夹，加载所有WAV文件
2. **客户端连接**：发送欢迎消息，但不自动开始音频流
3. **按需播放**：收到start_audio命令后开始发送音频数据
4. **循环播放**：音频文件播放完毕后自动循环到下一个文件
5. **动态控制**：可随时启动、停止或重新加载音频文件

## 测试验证

### 使用测试客户端：
```bash
python test_json_client.py
```

测试客户端会依次发送：
1. start_audio命令 - 启动音频流
2. 等待5秒让音频播放
3. stop_audio命令 - 停止音频流
4. reload_audio命令 - 重新加载音频文件
5. get_status命令 - 获取状态信息

### 预期日志输出：
```
[20:58:30] 发送命令消息: start_audio
[20:58:35] 发送命令消息: stop_audio
[20:58:37] 发送命令消息: reload_audio
[20:58:39] 发送命令消息: get_status
```

## 故障排除

### 1. 找不到音频文件
```
在 'audio' 文件夹中未找到音频文件，将使用模拟音频数据
```
**解决方案**：
- 确保audio文件夹存在
- 确保文件夹中有.wav或.WAV文件
- 使用`reload`命令重新扫描

### 2. 音频文件格式不支持
**解决方案**：
- 确保文件是WAV格式
- 建议使用16kHz采样率
- 可以使用音频转换工具转换格式

### 3. 音频流不启动
**解决方案**：
- 检查是否发送了start_audio命令
- 查看服务器日志确认命令是否收到
- 使用`status`命令检查audio_streaming状态

## 高级功能

### 1. 运行时添加音频文件
1. 将新的WAV文件复制到audio文件夹
2. 在服务器命令行输入`reload`
3. 或发送reload_audio JSON命令

### 2. 状态监控
发送get_status命令可获取详细状态：
```json
{
  "server_status": "running",
  "connected_clients": 1,
  "audio_files": 2,
  "current_file": 0,
  "audio_streaming": true,
  "seq_num": 1250
}
```

这样您就可以完全控制音频播放，不会再出现一连接就自动发送音频的问题了！
