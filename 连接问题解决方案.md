# WebSocket连接问题解决方案

## 问题描述

ESP32客户端频繁重连WebSocket服务器，导致：
- 服务器日志被大量连接信息刷屏
- 命令输入响应缓慢
- 资源浪费和性能下降

## 问题原因分析

1. **ESP32端问题**：
   - WebSocket连接不稳定
   - 网络环境不佳导致频繁断线重连
   - ESP32代码中可能存在连接管理问题

2. **服务器端问题**：
   - 缺乏连接保护机制
   - 没有重连频率限制
   - 命令输入处理被连接事件阻塞

## 解决方案

### ✅ **服务器端改进**

#### 1. 连接保护机制
```python
# 最大连接数限制
self.max_clients = 5

# 频繁重连检测（同一IP 10秒内最多3次连接）
if len(recent_connections) >= 3:
    await websocket.close(code=1008, reason="Too many connections")
```

#### 2. 重复连接检测
- 检测同一地址的重复连接
- 自动关闭重复连接
- 清理无效连接记录

#### 3. 命令输入优化
- 使用异步超时机制避免阻塞
- 添加连接状态监控命令
- 改进错误处理和日志输出

#### 4. 连接管理改进
- 心跳检测机制（60秒超时）
- 优雅的连接清理
- 连接历史记录管理

### 🔧 **ESP32端建议修改**

#### 1. 检查WebSocket客户端代码
```c
// 在sk_websocket.c中添加重连延迟
static void WebSocketReconnectDelay(void) {
    vTaskDelay(pdMS_TO_TICKS(5000));  // 5秒延迟
}

// 添加连接状态检查
static bool IsWebSocketConnected(void) {
    // 检查连接状态
    return (g_websocket.state == WS_STATE_CONNECTED);
}
```

#### 2. 改进连接管理
- 添加重连间隔（建议5-10秒）
- 实现指数退避重连策略
- 添加连接状态检查

#### 3. 网络稳定性检查
- 检查WiFi连接稳定性
- 确认网络配置正确
- 检查路由器设置

## 使用新的服务器功能

### 1. 连接监控命令
```bash
# 查看连接的客户端
clients

# 查看详细状态
status
```

### 2. 连接保护设置
服务器现在会：
- 限制最大连接数为5个
- 阻止同一IP频繁重连
- 自动清理无效连接

### 3. 改进的日志输出
```
[21:08:33] Client 1 connected: ('************', 62208)
[21:08:34] Too many connections from ************, rejecting...
[21:08:35] Client ('************', 62208) cleanup completed
```

## 测试和验证

### 1. 服务器端测试
```bash
# 启动服务器
python websocket_audio_server.py

# 测试命令
clients    # 查看连接的客户端
status     # 查看服务器状态
start      # 启动音频流
stop       # 停止音频流
```

### 2. 连接保护测试
- 尝试多个客户端同时连接
- 验证连接数限制是否生效
- 测试频繁重连保护机制

### 3. 性能测试
- 观察CPU和内存使用情况
- 测试命令响应速度
- 验证音频流稳定性

## 故障排除

### 1. 如果ESP32仍然频繁重连
**检查ESP32代码**：
- 查看WebSocket连接超时设置
- 检查网络错误处理逻辑
- 添加连接状态日志

**网络环境检查**：
- 确认WiFi信号强度
- 检查路由器设置
- 测试网络延迟和稳定性

### 2. 如果命令仍然响应慢
**服务器优化**：
- 检查是否有其他阻塞操作
- 增加异步处理
- 优化日志输出频率

### 3. 如果连接被误拒
**调整保护参数**：
```python
self.max_clients = 10        # 增加最大连接数
recent_connections >= 5      # 放宽重连限制
```

## 监控和维护

### 1. 日志监控
- 观察连接/断开频率
- 监控错误消息
- 记录性能指标

### 2. 定期维护
- 清理连接历史记录
- 重启服务器释放资源
- 更新保护参数

### 3. 性能优化
- 根据实际使用情况调整参数
- 优化音频发送频率
- 改进内存管理

## 总结

通过以上改进，服务器现在具备：
- ✅ 连接数限制和保护
- ✅ 频繁重连检测和阻止
- ✅ 改进的命令输入处理
- ✅ 更好的连接管理和清理
- ✅ 详细的监控和诊断功能

这些改进应该能显著减少频繁重连问题，提高服务器稳定性和响应速度。
